import request from '../plugins/axios'

/**
 * Get menu list
 * @returns {Promise}
 */
export function getMenuList() {
  // Use mock data in development mode
  if (import.meta.env.DEV && window.__MOCK_MENU_LIST__) {
    return Promise.resolve(window.__MOCK_MENU_LIST__());
  }

  return request({
    url: '/menu/list',
    method: 'get'
  })
}

// Add mock data for development
if (import.meta.env.DEV) {
  window.__MOCK_MENU_LIST__ = () => {
    return {
      code: 200,
      message: '成功',
      data: [
        {
          path: '/',
          name: 'Root',
          component: 'layout/DefaultLayout',
          redirect: '/dashboard',
          children: [
            {
              path: '/dashboard',
              name: 'Dashboard',
              component: 'views/home/<USER>',
              meta: {
                title: '首页',
                icon: 'dashboard',
                keepAlive: true
              }
            },
            {
              path: '/system/user',
              name: 'User',
              component: 'views/system/user/index',
              meta: {
                title: '用户管理',
                icon: 'user',
                keepAlive: true
              }
            },
            {
              path: '/system/role',
              name: 'Role',
              component: 'views/system/role/index',
              meta: {
                title: '角色管理',
                icon: 'tree',
                keepAlive: true
              }
            },
            {
              path: '/system/route',
              name: 'Route',
              component: 'views/system/route/index',
              meta: {
                title: '路由管理',
                icon: 'lock',
                keepAlive: true
              }
            },
            {
              path: '/system/dict',
              name: 'Dict',
              component: 'views/system/dict/index',
              meta: {
                title: '字典管理',
                icon: 'dict',
                keepAlive: true
              }
            },
            {
              path: '/about',
              name: 'About',
              component: 'views/about/index',
              meta: {
                title: '关于我们',
                icon: 'info',
                keepAlive: true
              }
            }
          ]
        }
      ]
    }
  }
}