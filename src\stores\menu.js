import { defineStore } from 'pinia'
import { getMenuList } from '../api/menu'

export const useMenuStore = defineStore('menu', {
  state: () => ({
    menuList: [],
    activeMenu: '',
    menuLoaded: false
  }),

  getters: {
    // Get menu as router format
    routerMenus: (state) => {
      return state.menuList.map(menu => formatMenuItem(menu)).flat()
    }
  },

  actions: {
    // Get menu list from API
    async getMenuList() {
      try {
        const response = await getMenuList()

        if (response.code === 200) {
          this.menuList = response.data
          this.menuLoaded = true
          return Promise.resolve(response.data)
        } else {
          return Promise.reject(response.message || 'Get menu list failed')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // Set active menu
    setActiveMenu(path) {
      // If path is root, set active menu to dashboard
      if (path === '/') {
        this.activeMenu = '/dashboard';
      } else {
        this.activeMenu = path;
      }
    },

    // Reset state
    resetState() {
      this.menuList = []
      this.activeMenu = ''
      this.menuLoaded = false
    }
  }
})

// Helper function to format menu items for router
const formatMenuItem = (item) => {
  // 如果是 layout/DefaultLayout，递归处理 children，返回 children 数组
  if (item.component === 'layout/DefaultLayout') {
    // 只处理 children，返回 children 路由数组
    return (item.children || []).map(child => formatMenuItem(child)).flat()
  }

  // 普通页面
  const route = {
    path: item.path,
    name: item.name,
    meta: { ...item.meta }
  }

  // Handle component
  if (item.component) {
    try {
      // 根据组件路径加载对应组件
      if (item.component === 'views/home/<USER>') {
        route.component = () => import('../views/home/<USER>')
      } else if (item.component === 'views/system/dict/index') {
        route.component = () => import('../views/system/dict/index.vue')
      } else if (item.component === 'views/system/role/index') {
        route.component = () => import('../views/system/role/index.vue')
      } else if (item.component === 'views/system/user/index') {
        route.component = () => import('../views/system/user/index.vue')
      } else if (item.component === 'views/system/route/index') {
        route.component = () => import('../views/system/route/index.vue')
      } else {
        // 对于其他组件，使用动态导入
        const componentPath = item.component.endsWith('.vue') ? item.component : `${item.component}.vue`
        route.component = () => import(/* @vite-ignore */ `../${componentPath}`)
      }
    } catch (e) {
      console.error(`Failed to load component for ${item.name}:`, e)
      // 如果组件加载失败，使用404页面作为回退
      route.component = () => import('../views/error/404.vue')
    }
  }

  // Handle redirect
  if (item.redirect) {
    route.redirect = item.redirect
  }

  // Handle children
  if (item.children && item.children.length > 0) {
    route.children = item.children.map(child => formatMenuItem(child)).flat()
  }

  return route
}